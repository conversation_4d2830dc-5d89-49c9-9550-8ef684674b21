#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV图像识别模块
功能：替代MaaFramework的图像识别功能，使用OpenCV实现模板匹配和OCR文字识别
作者：AI Assistant
日期：2025-08-05
"""

import cv2
import numpy as np
import pytesseract
import logging
from pathlib import Path
from typing import Tuple, Optional, List, Dict, Any
import os

class OpenCVRecognition:
    """OpenCV图像识别器 - 简单易懂的图像识别实现"""
    
    def __init__(self, image_path: str = "resource/image"):
        """
        初始化图像识别器
        
        Args:
            image_path: 图片资源文件夹路径
        """
        self.image_path = Path(image_path)
        self.logger = logging.getLogger(__name__)
        
        # 设置Tesseract路径（Windows用户可能需要修改）
        self._setup_tesseract()
        
        # 识别参数配置
        self.template_threshold = 0.8  # 模板匹配阈值
        self.ocr_confidence = 60       # OCR识别置信度阈值
        
        self.logger.info("OpenCV图像识别器初始化完成")
    
    def _setup_tesseract(self):
        """设置Tesseract OCR路径"""
        # 常见的Tesseract安装路径
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
            "/usr/bin/tesseract",  # Linux
            "/opt/homebrew/bin/tesseract",  # macOS
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                self.logger.info(f"找到Tesseract: {path}")
                return
        
        self.logger.warning("未找到Tesseract，OCR功能可能无法使用")
        self.logger.info("请安装Tesseract OCR: https://github.com/tesseract-ocr/tesseract")
    
    def capture_screen(self, device_controller) -> Optional[np.ndarray]:
        """
        截取设备屏幕
        
        Args:
            device_controller: 设备控制器实例
            
        Returns:
            截图的numpy数组，失败返回None
        """
        try:
            # 获取设备截图
            screenshot_data = device_controller.get_screenshot()
            if screenshot_data is None:
                return None
            
            # 将字节数据转换为OpenCV图像
            nparr = np.frombuffer(screenshot_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                self.logger.error("截图解码失败")
                return None
            
            self.logger.debug(f"截图成功，尺寸: {image.shape}")
            return image
            
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return None
    
    def template_match(self, screenshot: np.ndarray, template_name: str, 
                      threshold: float = None) -> Optional[Tuple[int, int, float]]:
        """
        模板匹配 - 在截图中查找模板图片
        
        Args:
            screenshot: 设备截图
            template_name: 模板图片文件名
            threshold: 匹配阈值，默认使用类设置
            
        Returns:
            匹配结果 (x, y, confidence) 或 None
        """
        try:
            # 加载模板图片
            template_path = self.image_path / template_name
            if not template_path.exists():
                self.logger.error(f"模板图片不存在: {template_path}")
                return None
            
            template = cv2.imread(str(template_path), cv2.IMREAD_COLOR)
            if template is None:
                self.logger.error(f"无法加载模板图片: {template_path}")
                return None
            
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 检查匹配度
            used_threshold = threshold if threshold is not None else self.template_threshold
            if max_val >= used_threshold:
                # 计算模板中心点坐标
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                self.logger.info(f"模板匹配成功: {template_name}, 位置: ({center_x}, {center_y}), 置信度: {max_val:.3f}")
                return (center_x, center_y, max_val)
            else:
                self.logger.debug(f"模板匹配失败: {template_name}, 最高置信度: {max_val:.3f} < {used_threshold}")
                return None
                
        except Exception as e:
            self.logger.error(f"模板匹配异常: {e}")
            return None
    
    def ocr_recognize(self, screenshot: np.ndarray, expected_text: str = None, 
                     region: Tuple[int, int, int, int] = None) -> Optional[Tuple[int, int, str]]:
        """
        OCR文字识别 - 在截图中查找指定文字
        
        Args:
            screenshot: 设备截图
            expected_text: 期望识别的文字
            region: 识别区域 (x, y, width, height)，None表示全屏
            
        Returns:
            识别结果 (x, y, text) 或 None
        """
        try:
            # 如果指定了区域，裁剪图像
            if region:
                x, y, w, h = region
                roi = screenshot[y:y+h, x:x+w]
            else:
                roi = screenshot
                x, y = 0, 0
            
            # 图像预处理 - 提高OCR识别率
            processed_image = self._preprocess_for_ocr(roi)
            
            # 执行OCR识别
            # 配置OCR参数：中文+英文识别
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
            
            # 获取详细识别结果
            data = pytesseract.image_to_data(processed_image, config=custom_config, output_type=pytesseract.Output.DICT)
            
            # 解析识别结果
            found_texts = []
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])
                
                if text and confidence > self.ocr_confidence:
                    # 计算文字中心坐标
                    text_x = x + data['left'][i] + data['width'][i] // 2
                    text_y = y + data['top'][i] + data['height'][i] // 2
                    
                    found_texts.append({
                        'text': text,
                        'x': text_x,
                        'y': text_y,
                        'confidence': confidence
                    })
            
            # 如果指定了期望文字，查找匹配项
            if expected_text:
                for item in found_texts:
                    if expected_text in item['text'] or item['text'] in expected_text:
                        self.logger.info(f"OCR识别成功: '{item['text']}' 位置: ({item['x']}, {item['y']}) 置信度: {item['confidence']}")
                        return (item['x'], item['y'], item['text'])
                
                self.logger.debug(f"未找到期望文字: '{expected_text}', 识别到的文字: {[item['text'] for item in found_texts]}")
                return None
            else:
                # 返回置信度最高的文字
                if found_texts:
                    best_match = max(found_texts, key=lambda x: x['confidence'])
                    self.logger.info(f"OCR识别结果: '{best_match['text']}' 位置: ({best_match['x']}, {best_match['y']})")
                    return (best_match['x'], best_match['y'], best_match['text'])
                else:
                    self.logger.debug("OCR未识别到任何文字")
                    return None
                    
        except Exception as e:
            self.logger.error(f"OCR识别异常: {e}")
            return None
    
    def _preprocess_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """
        OCR预处理 - 提高文字识别准确率
        
        Args:
            image: 输入图像
            
        Returns:
            处理后的图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 图像增强
        # 1. 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 2. 自适应阈值二值化
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 3. 形态学操作去除噪点
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def find_element(self, screenshot: np.ndarray, recognition_type: str, 
                    template: str = None, expected: str = None, 
                    region: Tuple[int, int, int, int] = None) -> Optional[Tuple[int, int]]:
        """
        通用元素查找 - 统一的识别接口
        
        Args:
            screenshot: 设备截图
            recognition_type: 识别类型 ("TemplateMatch" 或 "OCR")
            template: 模板图片名称（模板匹配用）
            expected: 期望文字（OCR用）
            region: 识别区域
            
        Returns:
            元素位置 (x, y) 或 None
        """
        if recognition_type == "TemplateMatch":
            if not template:
                self.logger.error("模板匹配需要指定template参数")
                return None
            
            result = self.template_match(screenshot, template)
            if result:
                return (result[0], result[1])  # 返回坐标，忽略置信度
            
        elif recognition_type == "OCR":
            if not expected:
                self.logger.error("OCR识别需要指定expected参数")
                return None
            
            result = self.ocr_recognize(screenshot, expected, region)
            if result:
                return (result[0], result[1])  # 返回坐标，忽略文字
        
        else:
            self.logger.error(f"不支持的识别类型: {recognition_type}")
        
        return None
    
    def save_debug_image(self, image: np.ndarray, filename: str, mark_point: Tuple[int, int] = None):
        """
        保存调试图片 - 用于问题排查
        
        Args:
            image: 要保存的图像
            filename: 文件名
            mark_point: 要标记的点坐标
        """
        try:
            debug_dir = Path("debug_images")
            debug_dir.mkdir(exist_ok=True)
            
            # 复制图像以避免修改原图
            debug_image = image.copy()
            
            # 如果指定了标记点，在图上画圆
            if mark_point:
                cv2.circle(debug_image, mark_point, 10, (0, 0, 255), 2)  # 红色圆圈
                cv2.putText(debug_image, f"({mark_point[0]}, {mark_point[1]})", 
                           (mark_point[0] + 15, mark_point[1] - 15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
            # 保存图片
            save_path = debug_dir / filename
            cv2.imwrite(str(save_path), debug_image)
            self.logger.debug(f"调试图片已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存调试图片失败: {e}")


# 使用示例和测试
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建识别器
    recognizer = OpenCVRecognition()
    
    # 测试模板匹配（需要有设备截图）
    print("OpenCV图像识别模块测试")
    print("注意：实际使用需要连接Android设备")
    
    # 模拟测试
    test_image = np.zeros((1080, 1920, 3), dtype=np.uint8)  # 模拟截图
    
    # 测试模板匹配
    result = recognizer.template_match(test_image, "应用伪装0.png")
    print(f"模板匹配测试结果: {result}")
    
    # 测试OCR识别
    result = recognizer.ocr_recognize(test_image, "华为视频")
    print(f"OCR识别测试结果: {result}")
    
    print("模块加载完成，可以与设备控制器配合使用")
