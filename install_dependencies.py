#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
功能：自动安装Python自动化任务系统所需的依赖包
作者：AI Assistant
日期：2025-08-05
"""

import subprocess
import sys
import os
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        return False
    else:
        print("✅ Python版本检查通过")
        return True

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"📦 正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def check_adb():
    """检查ADB是否可用"""
    try:
        result = subprocess.run(["adb", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ADB 已安装并可用")
            return True
        else:
            print("❌ ADB 不可用")
            return False
    except FileNotFoundError:
        print("❌ ADB 未找到")
        return False

def check_tesseract():
    """检查Tesseract OCR是否可用"""
    try:
        result = subprocess.run(["tesseract", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Tesseract OCR 已安装并可用")
            return True
        else:
            print("⚠️ Tesseract OCR 不可用")
            return False
    except FileNotFoundError:
        print("⚠️ Tesseract OCR 未找到")
        return False

def install_dependencies():
    """安装所有依赖"""
    print("Python自动化任务系统 - 依赖安装")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    print("\n📦 检查和安装Python包...")
    
    # 必需的包
    required_packages = [
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("pytesseract", "pytesseract"),
    ]
    
    success_count = 0
    for package, import_name in required_packages:
        if install_package(package, import_name):
            success_count += 1
    
    print(f"\n📊 Python包安装结果: {success_count}/{len(required_packages)} 成功")
    
    # 检查系统工具
    print("\n🔧 检查系统工具...")
    
    adb_ok = check_adb()
    tesseract_ok = check_tesseract()
    
    # 提供安装建议
    print("\n💡 安装建议:")
    
    if not adb_ok:
        system = platform.system()
        if system == "Windows":
            print("ADB安装方法:")
            print("1. 下载Android SDK Platform Tools")
            print("2. 解压到任意目录")
            print("3. 将目录添加到系统PATH环境变量")
            print("4. 或者安装Android Studio（包含ADB）")
        elif system == "Darwin":  # macOS
            print("ADB安装方法:")
            print("brew install android-platform-tools")
        else:  # Linux
            print("ADB安装方法:")
            print("sudo apt-get install android-tools-adb  # Ubuntu/Debian")
            print("sudo yum install android-tools        # CentOS/RHEL")
    
    if not tesseract_ok:
        system = platform.system()
        if system == "Windows":
            print("\nTesseract OCR安装方法:")
            print("1. 访问: https://github.com/tesseract-ocr/tesseract")
            print("2. 下载Windows安装包")
            print("3. 安装时选择中文语言包")
            print("4. 确保添加到系统PATH")
        elif system == "Darwin":  # macOS
            print("\nTesseract OCR安装方法:")
            print("brew install tesseract")
        else:  # Linux
            print("\nTesseract OCR安装方法:")
            print("sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim  # Ubuntu/Debian")
            print("sudo yum install tesseract tesseract-langpack-chi-sim     # CentOS/RHEL")
    
    # 总结
    print("\n📋 安装总结:")
    if success_count == len(required_packages):
        print("✅ 所有Python依赖包安装成功")
    else:
        print("❌ 部分Python依赖包安装失败")
    
    if adb_ok:
        print("✅ ADB工具可用")
    else:
        print("❌ ADB工具不可用（必需）")
    
    if tesseract_ok:
        print("✅ Tesseract OCR可用")
    else:
        print("⚠️ Tesseract OCR不可用（可选，影响文字识别功能）")
    
    # 检查是否可以启动系统
    can_start = (success_count == len(required_packages) and adb_ok)
    
    if can_start:
        print("\n🎉 系统可以启动！运行以下命令开始使用:")
        print("python main.py")
    else:
        print("\n⚠️ 系统暂时无法启动，请先解决上述依赖问题")
    
    return can_start

def create_requirements_txt():
    """创建requirements.txt文件"""
    requirements = """# Python自动化任务系统依赖包
opencv-python>=4.5.0
numpy>=1.19.0
pytesseract>=0.3.8

# 可选依赖（用于图像处理优化）
Pillow>=8.0.0
"""
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements)
    
    print("✅ 已创建 requirements.txt 文件")
    print("可以使用以下命令安装依赖:")
    print("pip install -r requirements.txt")

def main():
    """主函数"""
    try:
        # 安装依赖
        success = install_dependencies()
        
        # 创建requirements.txt
        create_requirements_txt()
        
        # 等待用户确认
        input("\n按回车键退出...")
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n用户中断安装")
        return False
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    main()
