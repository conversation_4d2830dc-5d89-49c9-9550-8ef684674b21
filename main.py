#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaaFramework自动化任务系统 - 主启动文件
功能：整合所有模块，提供统一的启动入口
作者：AI Assistant
日期：2025-08-05
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from simple_automation_engine import SimpleAutomationEngine
    from simple_automation_ui import SimpleAutomationUI
    from maa_ui_design import MaaDesignUI
    from opencv_recognition import OpenCVRecognition
    from android_controller import AndroidController
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

class MaaLauncher:
    """MaaFramework自动化任务系统启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('maa_launcher.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def show_launcher_ui(self):
        """显示启动器界面"""
        root = tk.Tk()
        root.title("MaaFramework自动化任务系统 - 启动器")
        root.geometry("500x350")
        root.resizable(False, False)
        
        # 设置窗口居中
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (500 // 2)
        y = (root.winfo_screenheight() // 2) - (350 // 2)
        root.geometry(f"500x350+{x}+{y}")
        
        # 主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="MaaFramework自动化任务系统",
            font=('Microsoft YaHei', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # 描述
        desc_label = ttk.Label(
            main_frame,
            text="选择要启动的界面模式：",
            font=('Microsoft YaHei', 12)
        )
        desc_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 简化功能界面按钮
        simple_ui_button = ttk.Button(
            button_frame,
            text="简化功能界面 (推荐)",
            command=lambda: self.launch_ui('simple', root),
            width=20
        )
        simple_ui_button.pack(pady=5)

        # 简洁设计界面按钮
        design_ui_button = ttk.Button(
            button_frame,
            text="简洁设计界面",
            command=lambda: self.launch_ui('design', root),
            width=20
        )
        design_ui_button.pack(pady=5)
        
        # 命令行模式按钮
        cli_button = ttk.Button(
            button_frame,
            text="命令行模式",
            command=lambda: self.launch_cli(root),
            width=20
        )
        cli_button.pack(pady=5)
        
        # 测试配置按钮
        test_button = ttk.Button(
            button_frame,
            text="测试配置",
            command=lambda: self.test_configuration(root),
            width=20
        )
        test_button.pack(pady=5)
        
        # 信息框架
        info_frame = ttk.LabelFrame(main_frame, text="系统信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 系统信息
        info_text = tk.Text(info_frame, height=6, width=50, wrap=tk.WORD)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加系统信息
        info_content = self.get_system_info()
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        root.mainloop()
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # 检查资源文件
            engine = SimpleAutomationEngine()
            pipeline_exists = (engine.resource_path / "pipeline" / "pipeline.json").exists()
            email_file_exists = (engine.resource_path / "pipeline" / "邮箱.txt").exists()

            info = f"""资源配置状态：
• Pipeline配置: {'✓ 存在' if pipeline_exists else '✗ 不存在'}
• 邮箱文件: {'✓ 存在' if email_file_exists else '✗ 不存在'}
• 任务数量: {len(engine.pipeline_config)}
• 技术栈: OpenCV + Python + ADB

系统版本: v2.0.0 (简化版)
开发者: AI Assistant"""

            return info

        except Exception as e:
            return f"获取系统信息失败: {e}"
    
    def launch_ui(self, ui_type, parent_root):
        """启动UI界面"""
        try:
            parent_root.destroy()

            if ui_type == 'simple':
                self.logger.info("启动简化功能界面")
                app = SimpleAutomationUI()
            elif ui_type == 'design':
                self.logger.info("启动简洁设计界面")
                app = MaaDesignUI()
            else:
                raise ValueError(f"未知的UI类型: {ui_type}")

            app.run()
            
        except Exception as e:
            self.logger.error(f"启动UI失败: {e}")
            messagebox.showerror("错误", f"启动UI失败: {e}")
    
    def launch_cli(self, parent_root):
        """启动命令行模式"""
        try:
            parent_root.destroy()
            self.logger.info("启动命令行模式")
            
            print("\n" + "="*50)
            print("MaaFramework自动化任务系统 - 命令行模式")
            print("="*50)

            # 创建自动化引擎
            engine = SimpleAutomationEngine()
            
            # 显示任务序列
            sequence = engine.get_task_sequence()
            print(f"\n任务序列 (共{len(sequence)}个任务):")
            for i, task in enumerate(sequence, 1):
                print(f"  {i}. {task}")

            # 连接设备
            if not engine.connect_device():
                print("\n设备连接失败，无法执行任务")
                input("按回车键退出...")
                return

            # 询问是否执行
            choice = input("\n是否开始执行任务序列? (y/n): ").lower()

            if choice == 'y':
                print("\n开始执行任务...")

                def cli_callback(task_name, status, current, total):
                    print(f"[{current}/{total}] {task_name}: {status}")

                success = engine.execute_task_sequence(callback=cli_callback)
                
                if success:
                    print("\n✓ 所有任务执行完成！")
                else:
                    print("\n✗ 任务执行失败！")
            else:
                print("\n任务执行已取消")
            
            input("\n按回车键退出...")
            
        except Exception as e:
            self.logger.error(f"命令行模式执行失败: {e}")
            print(f"错误: {e}")
            input("按回车键退出...")
    
    def test_configuration(self, parent_root):
        """测试配置"""
        try:
            self.logger.info("开始测试配置")
            
            # 创建测试窗口
            test_window = tk.Toplevel(parent_root)
            test_window.title("配置测试")
            test_window.geometry("600x400")
            
            # 测试结果显示
            result_text = tk.Text(test_window, wrap=tk.WORD)
            result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 执行测试
            result_text.insert(tk.END, "开始配置测试...\n\n")
            test_window.update()
            
            # 测试自动化引擎
            result_text.insert(tk.END, "1. 测试自动化引擎...\n")
            test_window.update()

            try:
                engine = SimpleAutomationEngine()
                result_text.insert(tk.END, "   ✓ 自动化引擎初始化成功\n")

                # 验证配置
                errors = engine.validate_configuration()
                if errors:
                    result_text.insert(tk.END, f"   ⚠ 发现配置问题: {len(errors)}个\n")
                    for error in errors[:3]:  # 只显示前3个错误
                        result_text.insert(tk.END, f"     - {error}\n")
                else:
                    result_text.insert(tk.END, "   ✓ 配置验证通过\n")

            except Exception as e:
                result_text.insert(tk.END, f"   ✗ 自动化引擎测试失败: {e}\n")
            
            test_window.update()
            
            # 测试图像识别
            result_text.insert(tk.END, "\n2. 测试图像识别...\n")
            test_window.update()

            try:
                recognizer = OpenCVRecognition()
                result_text.insert(tk.END, "   ✓ OpenCV识别器初始化成功\n")

                # 检查图片资源
                image_path = recognizer.image_path
                if image_path.exists():
                    png_files = list(image_path.glob("*.png"))
                    result_text.insert(tk.END, f"   ✓ 找到图片资源: {len(png_files)}个\n")
                else:
                    result_text.insert(tk.END, "   ⚠ 图片资源目录不存在\n")

            except Exception as e:
                result_text.insert(tk.END, f"   ✗ 图像识别测试失败: {e}\n")
            
            test_window.update()
            
            # 测试设备控制
            result_text.insert(tk.END, "\n3. 测试设备控制...\n")
            test_window.update()

            try:
                controller = AndroidController()
                result_text.insert(tk.END, "   ✓ Android控制器初始化成功\n")

                sequence = engine.get_task_sequence()
                result_text.insert(tk.END, f"   ✓ 任务序列生成成功，共{len(sequence)}个任务\n")

            except Exception as e:
                result_text.insert(tk.END, f"   ✗ 设备控制测试失败: {e}\n")
            
            result_text.insert(tk.END, "\n配置测试完成！\n")
            result_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.logger.error(f"配置测试失败: {e}")
            messagebox.showerror("错误", f"配置测试失败: {e}")


def main():
    """主函数"""
    try:
        launcher = MaaLauncher()
        launcher.show_launcher_ui()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
