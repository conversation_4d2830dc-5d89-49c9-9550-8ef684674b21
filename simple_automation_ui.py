#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化自动化任务系统UI界面
功能：基于OpenCV+Python的新手友好UI界面
作者：AI Assistant
日期：2025-08-05
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import logging
from simple_automation_engine import SimpleAutomationEngine

class SimpleAutomationUI:
    """简化自动化任务系统UI界面 - 新手友好版本"""
    
    def __init__(self):
        """初始化UI界面"""
        self.root = tk.Tk()
        self.root.title("Python自动化任务系统 (OpenCV版)")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 初始化自动化引擎
        self.engine = None
        self.is_running = False
        
        # 创建UI组件
        self._create_widgets()
        self._setup_layout()
        
        # 设置日志
        self._setup_logging()
        
        # 尝试初始化引擎
        self._initialize_engine()
    
    def _create_widgets(self):
        """创建UI组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # 设备连接区域
        self.device_frame = ttk.LabelFrame(self.main_frame, text="设备连接", padding="5")
        
        self.device_address_var = tk.StringVar(value="127.0.0.1:5555")
        ttk.Label(self.device_frame, text="设备地址:").grid(row=0, column=0, sticky="w")
        self.device_entry = ttk.Entry(self.device_frame, textvariable=self.device_address_var, width=20)
        self.connect_button = ttk.Button(self.device_frame, text="连接设备", command=self._connect_device)
        self.disconnect_button = ttk.Button(self.device_frame, text="断开连接", command=self._disconnect_device, state="disabled")
        
        self.device_status_var = tk.StringVar(value="未连接")
        self.device_status_label = ttk.Label(self.device_frame, textvariable=self.device_status_var, foreground="red")
        
        # 任务控制区域
        self.control_frame = ttk.LabelFrame(self.main_frame, text="任务控制", padding="5")
        
        self.start_button = ttk.Button(self.control_frame, text="开始执行", command=self._start_execution, state="disabled")
        self.stop_button = ttk.Button(self.control_frame, text="停止执行", command=self._stop_execution, state="disabled")
        self.validate_button = ttk.Button(self.control_frame, text="验证配置", command=self._validate_config)
        
        # 进度显示
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.control_frame, variable=self.progress_var, maximum=100)
        
        self.current_task_var = tk.StringVar(value="当前任务: 无")
        self.current_task_label = ttk.Label(self.control_frame, textvariable=self.current_task_var)
        
        # 任务列表区域
        self.task_frame = ttk.LabelFrame(self.main_frame, text="任务列表", padding="5")
        
        # 任务列表
        self.task_listbox = tk.Listbox(self.task_frame, height=8)
        self.task_scrollbar = ttk.Scrollbar(self.task_frame, orient="vertical", command=self.task_listbox.yview)
        self.task_listbox.configure(yscrollcommand=self.task_scrollbar.set)
        
        # 日志显示区域
        self.log_frame = ttk.LabelFrame(self.main_frame, text="执行日志", padding="5")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=12, width=80)
        
        # 帮助信息区域
        self.help_frame = ttk.LabelFrame(self.main_frame, text="使用说明", padding="5")
        
        help_text = """使用步骤：
1. 确保Android设备开启USB调试并连接到电脑
2. 点击"连接设备"按钮连接Android设备
3. 点击"验证配置"检查任务配置是否正确
4. 点击"开始执行"运行自动化任务
5. 观察执行日志了解任务进度

注意事项：
• 首次连接需要在设备上授权USB调试
• 确保设备屏幕保持亮屏状态
• 任务执行过程中请勿操作设备"""
        
        self.help_text = tk.Text(self.help_frame, height=8, width=50, wrap=tk.WORD)
        self.help_text.insert(tk.END, help_text)
        self.help_text.config(state=tk.DISABLED)
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # 设备连接区域
        self.device_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        self.device_entry.grid(row=0, column=1, padx=(5, 5))
        self.connect_button.grid(row=0, column=2, padx=(0, 5))
        self.disconnect_button.grid(row=0, column=3, padx=(0, 5))
        self.device_status_label.grid(row=0, column=4, padx=(10, 0))
        
        # 任务控制区域
        self.control_frame.grid(row=1, column=0, sticky="ew", padx=(0, 10), pady=(0, 10))
        self.current_task_label.grid(row=0, column=0, columnspan=3, pady=(0, 5))
        self.start_button.grid(row=1, column=0, padx=(0, 5))
        self.stop_button.grid(row=1, column=1, padx=(0, 5))
        self.validate_button.grid(row=1, column=2)
        self.progress_bar.grid(row=2, column=0, columnspan=3, sticky="ew", pady=(10, 0))
        
        # 任务列表和帮助信息
        self.task_frame.grid(row=1, column=1, sticky="nsew", pady=(0, 10))
        self.task_listbox.grid(row=0, column=0, sticky="nsew")
        self.task_scrollbar.grid(row=0, column=1, sticky="ns")
        
        self.help_frame.grid(row=2, column=1, sticky="nsew")
        self.help_text.grid(row=0, column=0, sticky="nsew")
        
        # 日志区域
        self.log_frame.grid(row=2, column=0, sticky="nsew")
        self.log_text.grid(row=0, column=0, sticky="nsew")
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=2)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(2, weight=1)
        
        self.task_frame.columnconfigure(0, weight=1)
        self.task_frame.rowconfigure(0, weight=1)
        
        self.help_frame.columnconfigure(0, weight=1)
        self.help_frame.rowconfigure(0, weight=1)
        
        self.log_frame.columnconfigure(0, weight=1)
        self.log_frame.rowconfigure(0, weight=1)
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器，将日志输出到UI
        class UILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
            
            def emit(self, record):
                msg = self.format(record)
                # 在主线程中更新UI
                self.text_widget.after(0, self._append_log, msg)
            
            def _append_log(self, msg):
                self.text_widget.insert(tk.END, msg + "\n")
                self.text_widget.see(tk.END)
        
        # 配置日志
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        
        # 添加UI日志处理器
        ui_handler = UILogHandler(self.log_text)
        ui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(ui_handler)
    
    def _initialize_engine(self):
        """初始化自动化引擎"""
        try:
            device_address = self.device_address_var.get()
            self.engine = SimpleAutomationEngine(device_address=device_address)
            
            # 加载任务列表
            self._update_task_list()
            
            self._log_message("自动化引擎初始化成功")
            
        except Exception as e:
            self._log_message(f"引擎初始化失败: {e}")
            messagebox.showerror("错误", f"引擎初始化失败: {e}")
    
    def _update_task_list(self):
        """更新任务列表"""
        if not self.engine:
            return
        
        # 清空列表
        self.task_listbox.delete(0, tk.END)
        
        # 获取任务序列
        sequence = self.engine.get_task_sequence()
        
        # 添加任务到列表
        for i, task_name in enumerate(sequence, 1):
            self.task_listbox.insert(tk.END, f"{i}. {task_name}")
    
    def _connect_device(self):
        """连接设备"""
        if not self.engine:
            messagebox.showerror("错误", "引擎未初始化")
            return
        
        self.connect_button.config(state="disabled")
        self._log_message("正在连接设备...")
        
        # 在新线程中连接设备
        def connect_thread():
            success = self.engine.connect_device()
            self.root.after(0, self._on_device_connected, success)
        
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def _on_device_connected(self, success):
        """设备连接完成回调"""
        if success:
            self.device_status_var.set("已连接")
            self.device_status_label.config(foreground="green")
            self.connect_button.config(state="disabled")
            self.disconnect_button.config(state="normal")
            self.start_button.config(state="normal")
            self._log_message("设备连接成功")
        else:
            self.device_status_var.set("连接失败")
            self.device_status_label.config(foreground="red")
            self.connect_button.config(state="normal")
            self._log_message("设备连接失败")
    
    def _disconnect_device(self):
        """断开设备连接"""
        if self.engine:
            self.engine.disconnect_device()
        
        self.device_status_var.set("未连接")
        self.device_status_label.config(foreground="red")
        self.connect_button.config(state="normal")
        self.disconnect_button.config(state="disabled")
        self.start_button.config(state="disabled")
        self._log_message("设备连接已断开")
    
    def _start_execution(self):
        """开始执行任务"""
        if not self.engine or not self.engine.controller.is_connected:
            messagebox.showwarning("警告", "请先连接设备")
            return
        
        if self.is_running:
            messagebox.showwarning("警告", "任务正在执行中")
            return
        
        self.is_running = True
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        
        # 在新线程中执行任务
        def execution_thread():
            try:
                def status_callback(task_name, status, current, total):
                    self.root.after(0, self._update_execution_status, task_name, status, current, total)
                
                success = self.engine.execute_task_sequence(callback=status_callback)
                self.root.after(0, self._on_execution_completed, success)
                
            except Exception as e:
                self.root.after(0, self._on_execution_error, str(e))
        
        threading.Thread(target=execution_thread, daemon=True).start()
    
    def _stop_execution(self):
        """停止执行任务"""
        if self.engine:
            self.engine.stop_execution()
        
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.current_task_var.set("当前任务: 无")
        self._log_message("任务执行已停止")
    
    def _update_execution_status(self, task_name, status, current, total):
        """更新执行状态"""
        # 更新进度条
        progress = (current / total) * 100
        self.progress_var.set(progress)
        
        # 更新当前任务
        self.current_task_var.set(f"当前任务: {task_name} ({status})")
        
        # 高亮当前任务
        for i in range(self.task_listbox.size()):
            item_text = self.task_listbox.get(i)
            if task_name in item_text:
                self.task_listbox.selection_clear(0, tk.END)
                self.task_listbox.selection_set(i)
                self.task_listbox.see(i)
                break
    
    def _on_execution_completed(self, success):
        """任务执行完成"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.current_task_var.set("当前任务: 无")
        
        if success:
            self.progress_var.set(100)
            self._log_message("所有任务执行完成！")
            messagebox.showinfo("完成", "所有任务执行完成！")
        else:
            self._log_message("任务执行失败！")
            messagebox.showerror("错误", "任务执行失败！")
    
    def _on_execution_error(self, error_msg):
        """任务执行出错"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.current_task_var.set("当前任务: 无")
        
        self._log_message(f"执行出错: {error_msg}")
        messagebox.showerror("错误", f"执行出错: {error_msg}")
    
    def _validate_config(self):
        """验证配置"""
        if not self.engine:
            messagebox.showwarning("警告", "引擎未初始化")
            return
        
        errors = self.engine.validate_configuration()
        if errors:
            error_msg = "\n".join(errors)
            messagebox.showwarning("配置验证", f"发现以下问题:\n{error_msg}")
            self._log_message(f"配置验证失败，发现{len(errors)}个问题")
        else:
            messagebox.showinfo("配置验证", "配置验证通过！")
            self._log_message("配置验证通过")
    
    def _log_message(self, message):
        """记录日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        logging.info(log_entry)
    
    def run(self):
        """运行UI界面"""
        self.root.mainloop()


# 使用示例
if __name__ == "__main__":
    app = SimpleAutomationUI()
    app.run()
