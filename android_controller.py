#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android设备控制模块
功能：替代MaaFramework的设备控制功能，使用ADB实现Android设备的连接和操作
作者：AI Assistant
日期：2025-08-05
"""

import subprocess
import time
import logging
from typing import Optional, Tuple, List
from pathlib import Path
import tempfile
import os

class AndroidController:
    """Android设备控制器 - 简单易懂的设备控制实现"""
    
    def __init__(self, device_address: str = "127.0.0.1:5555", adb_path: str = "adb"):
        """
        初始化Android设备控制器
        
        Args:
            device_address: 设备地址，默认为本地模拟器
            adb_path: ADB可执行文件路径
        """
        self.device_address = device_address
        self.adb_path = adb_path
        self.logger = logging.getLogger(__name__)
        self.is_connected = False
        
        # 检查ADB是否可用
        self._check_adb_available()
        
        self.logger.info(f"Android控制器初始化完成，目标设备: {device_address}")
    
    def _check_adb_available(self):
        """检查ADB是否可用"""
        try:
            result = subprocess.run([self.adb_path, "version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.logger.info(f"ADB可用: {result.stdout.split()[4]}")  # 显示ADB版本
            else:
                self.logger.error("ADB不可用，请检查ADB安装")
        except Exception as e:
            self.logger.error(f"ADB检查失败: {e}")
            self.logger.info("请确保ADB已安装并添加到系统PATH")
    
    def connect(self) -> bool:
        """
        连接到Android设备
        
        Returns:
            连接是否成功
        """
        try:
            self.logger.info(f"正在连接设备: {self.device_address}")
            
            # 如果是网络地址，先尝试连接
            if ":" in self.device_address:
                connect_result = subprocess.run(
                    [self.adb_path, "connect", self.device_address],
                    capture_output=True, text=True, timeout=30
                )
                self.logger.debug(f"连接结果: {connect_result.stdout}")
            
            # 检查设备是否在线
            devices_result = subprocess.run(
                [self.adb_path, "devices"],
                capture_output=True, text=True, timeout=10
            )
            
            if devices_result.returncode == 0:
                devices_output = devices_result.stdout
                if self.device_address in devices_output and "device" in devices_output:
                    self.is_connected = True
                    self.logger.info("设备连接成功")
                    
                    # 获取设备信息
                    self._get_device_info()
                    return True
                else:
                    self.logger.error(f"设备未找到或未授权: {self.device_address}")
                    self.logger.info("请检查设备是否开启USB调试，并授权此计算机")
                    return False
            else:
                self.logger.error("ADB devices命令执行失败")
                return False
                
        except Exception as e:
            self.logger.error(f"设备连接失败: {e}")
            return False
    
    def _get_device_info(self):
        """获取设备信息"""
        try:
            # 获取设备型号
            model_result = self._run_adb_command(["shell", "getprop", "ro.product.model"])
            if model_result:
                self.logger.info(f"设备型号: {model_result.strip()}")
            
            # 获取Android版本
            version_result = self._run_adb_command(["shell", "getprop", "ro.build.version.release"])
            if version_result:
                self.logger.info(f"Android版本: {version_result.strip()}")
            
            # 获取屏幕分辨率
            size_result = self._run_adb_command(["shell", "wm", "size"])
            if size_result:
                self.logger.info(f"屏幕分辨率: {size_result.strip()}")
                
        except Exception as e:
            self.logger.debug(f"获取设备信息失败: {e}")
    
    def _run_adb_command(self, command: List[str], timeout: int = 30) -> Optional[str]:
        """
        执行ADB命令
        
        Args:
            command: ADB命令参数列表
            timeout: 超时时间（秒）
            
        Returns:
            命令输出或None
        """
        try:
            full_command = [self.adb_path, "-s", self.device_address] + command
            result = subprocess.run(
                full_command,
                capture_output=True, text=True, timeout=timeout
            )
            
            if result.returncode == 0:
                return result.stdout
            else:
                self.logger.error(f"ADB命令执行失败: {' '.join(command)}, 错误: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"ADB命令超时: {' '.join(command)}")
            return None
        except Exception as e:
            self.logger.error(f"ADB命令异常: {e}")
            return None
    
    def get_screenshot(self) -> Optional[bytes]:
        """
        获取设备截图
        
        Returns:
            截图的字节数据或None
        """
        if not self.is_connected:
            self.logger.error("设备未连接")
            return None
        
        try:
            # 使用临时文件保存截图
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # 执行截图命令
            screenshot_result = self._run_adb_command([
                "exec-out", "screencap", "-p"
            ])
            
            if screenshot_result:
                # 将截图数据写入临时文件
                with open(temp_path, 'wb') as f:
                    f.write(screenshot_result.encode('latin1'))
                
                # 读取截图数据
                with open(temp_path, 'rb') as f:
                    screenshot_data = f.read()
                
                # 清理临时文件
                os.unlink(temp_path)
                
                self.logger.debug("截图获取成功")
                return screenshot_data
            else:
                self.logger.error("截图命令执行失败")
                return None
                
        except Exception as e:
            self.logger.error(f"获取截图失败: {e}")
            return None
    
    def click(self, x: int, y: int) -> bool:
        """
        点击屏幕指定位置
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            操作是否成功
        """
        if not self.is_connected:
            self.logger.error("设备未连接")
            return False
        
        try:
            result = self._run_adb_command([
                "shell", "input", "tap", str(x), str(y)
            ])
            
            if result is not None:
                self.logger.info(f"点击成功: ({x}, {y})")
                return True
            else:
                self.logger.error(f"点击失败: ({x}, {y})")
                return False
                
        except Exception as e:
            self.logger.error(f"点击操作异常: {e}")
            return False
    
    def input_text(self, text: str) -> bool:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            
        Returns:
            操作是否成功
        """
        if not self.is_connected:
            self.logger.error("设备未连接")
            return False
        
        try:
            # 转义特殊字符
            escaped_text = text.replace(' ', '%s').replace('&', '\\&')
            
            result = self._run_adb_command([
                "shell", "input", "text", escaped_text
            ])
            
            if result is not None:
                self.logger.info(f"文本输入成功: {text}")
                return True
            else:
                self.logger.error(f"文本输入失败: {text}")
                return False
                
        except Exception as e:
            self.logger.error(f"文本输入异常: {e}")
            return False
    
    def start_app(self, package_name: str) -> bool:
        """
        启动应用
        
        Args:
            package_name: 应用包名
            
        Returns:
            操作是否成功
        """
        if not self.is_connected:
            self.logger.error("设备未连接")
            return False
        
        try:
            # 使用monkey命令启动应用
            result = self._run_adb_command([
                "shell", "monkey", "-p", package_name, "-c", "android.intent.category.LAUNCHER", "1"
            ])
            
            if result is not None:
                self.logger.info(f"应用启动成功: {package_name}")
                return True
            else:
                self.logger.error(f"应用启动失败: {package_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"应用启动异常: {e}")
            return False
    
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 500) -> bool:
        """
        滑动操作
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 滑动持续时间（毫秒）
            
        Returns:
            操作是否成功
        """
        if not self.is_connected:
            self.logger.error("设备未连接")
            return False
        
        try:
            result = self._run_adb_command([
                "shell", "input", "swipe", 
                str(start_x), str(start_y), str(end_x), str(end_y), str(duration)
            ])
            
            if result is not None:
                self.logger.info(f"滑动成功: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
                return True
            else:
                self.logger.error(f"滑动失败: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
                return False
                
        except Exception as e:
            self.logger.error(f"滑动操作异常: {e}")
            return False
    
    def press_key(self, keycode: int) -> bool:
        """
        按键操作
        
        Args:
            keycode: 按键码（如4=返回键，3=Home键，82=菜单键）
            
        Returns:
            操作是否成功
        """
        if not self.is_connected:
            self.logger.error("设备未连接")
            return False
        
        try:
            result = self._run_adb_command([
                "shell", "input", "keyevent", str(keycode)
            ])
            
            if result is not None:
                self.logger.info(f"按键成功: {keycode}")
                return True
            else:
                self.logger.error(f"按键失败: {keycode}")
                return False
                
        except Exception as e:
            self.logger.error(f"按键操作异常: {e}")
            return False
    
    def wait(self, seconds: float):
        """
        等待指定时间
        
        Args:
            seconds: 等待时间（秒）
        """
        self.logger.debug(f"等待 {seconds} 秒")
        time.sleep(seconds)
    
    def disconnect(self):
        """断开设备连接"""
        if self.is_connected:
            try:
                if ":" in self.device_address:
                    subprocess.run([self.adb_path, "disconnect", self.device_address], 
                                 capture_output=True, timeout=10)
                self.is_connected = False
                self.logger.info("设备连接已断开")
            except Exception as e:
                self.logger.error(f"断开连接失败: {e}")
    
    def get_device_status(self) -> dict:
        """
        获取设备状态信息
        
        Returns:
            设备状态字典
        """
        return {
            'connected': self.is_connected,
            'address': self.device_address,
            'adb_path': self.adb_path
        }


# 使用示例和测试
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建设备控制器
    controller = AndroidController()
    
    print("Android设备控制器测试")
    print("=" * 30)
    
    # 测试连接
    if controller.connect():
        print("✓ 设备连接成功")
        
        # 测试截图
        screenshot = controller.get_screenshot()
        if screenshot:
            print("✓ 截图获取成功")
        else:
            print("✗ 截图获取失败")
        
        # 显示设备状态
        status = controller.get_device_status()
        print(f"设备状态: {status}")
        
        # 断开连接
        controller.disconnect()
    else:
        print("✗ 设备连接失败")
        print("请检查:")
        print("1. Android设备是否开启USB调试")
        print("2. 设备是否授权此计算机")
        print("3. ADB是否正确安装")
    
    print("\n模块加载完成，可以与图像识别器配合使用")
