{"启动应用伪装": {"action": "StartApp", "package": "com.variable.apkhook", "next": ["点击配置应用"]}, "点击配置应用": {"recognition": "TemplateMatch", "template": "应用伪装0.png", "action": "Click", "next": ["点击华为视频"]}, "点击华为视频": {"recognition": "OCR", "expected": "华为视频", "action": "Click", "next": ["点击右上角"]}, "点击右上角": {"recognition": "TemplateMatch", "template": "应用伪装1.png", "action": "Click", "next": ["点击清除应用数据"]}, "点击清除应用数据": {"recognition": "OCR", "expected": "清除应用数据", "action": "Click", "next": ["点击保存"]}, "点击保存": {"recognition": "OCR", "expected": "保存", "action": "Click", "next": ["点击停止"]}, "点击停止": {"recognition": "TemplateMatch", "template": "应用伪装2.png", "action": "Click", "next": ["启动华为视频"]}, "启动华为视频": {"action": "StartApp", "package": "com.huawei.himovie", "next": ["点击同意"]}, "点击同意": {"recognition": "OCR", "expected": "同意", "action": "Click", "pre_wait_freezes": {"time": 2000}, "next": ["点击取消"]}, "点击取消": {"recognition": "TemplateMatch", "template": "取消.png", "action": "Click", "pre_wait_freezes": {"time": 2000}, "next": ["点击我的"]}, "点击我的": {"recognition": "OCR", "expected": "我的", "action": "Click", "pre_wait_freezes": {"time": 2000}, "next": ["点击未登录"]}, "点击未登录": {"recognition": "OCR", "expected": "未登录", "action": "Click", "next": ["点击继续使用"]}, "点击继续使用": {"recognition": "OCR", "expected": "继续使用", "action": "Click", "next": ["点击密码登录"]}, "点击密码登录": {"recognition": "OCR", "expected": "密码登录", "action": "Click", "next": ["点击继续"]}, "点击继续": {"recognition": "OCR", "expected": "继续", "action": "Click", "next": ["点击手机号"]}, "点击手机号": {"recognition": "OCR", "expected": "手机号", "action": "Click", "next": ["输入邮件地址"]}, "输入邮件地址": {"recognition": "OCR", "expected": "邮件地址", "action": "ReadEmailFromFile", "file_name": "邮箱.txt", "line_number": 1, "standard_action": "InputText"}}