# Python自动化任务系统 - 新手使用指南

## 🎯 项目简介

这是一个**新手友好**的Python自动化任务系统，使用简单易懂的技术栈：
- **OpenCV**: 图像识别（替代复杂的MaaFramework）
- **ADB**: Android设备控制（直接使用系统命令）
- **Tkinter**: 图形界面（Python内置，无需额外安装）

## 🚀 快速开始

### 1. 环境准备

#### 必需软件
```bash
# Python 3.7+ (推荐3.8+)
python --version

# 安装依赖包
pip install opencv-python
pip install pytesseract
pip install numpy
```

#### Android调试工具
- **ADB**: Android调试桥（通常随Android SDK安装）
- **USB调试**: 在Android设备上开启USB调试

#### OCR识别工具（可选）
- **Tesseract**: 文字识别引擎
  - Windows: 下载安装包 https://github.com/tesseract-ocr/tesseract
  - 安装后确保添加到系统PATH

### 2. 启动系统

```bash
# 进入项目目录
cd demo

# 启动系统
python main.py
```

### 3. 选择界面模式

启动后会显示选择界面：
- **简化功能界面 (推荐)**: 新手友好的完整功能界面
- **简洁设计界面**: 基于原设计图的简洁界面
- **命令行模式**: 适合调试和批量执行
- **测试配置**: 验证系统配置

## 📱 设备连接指南

### Android设备设置

1. **开启开发者选项**
   - 设置 → 关于手机 → 连续点击"版本号"7次
   - 返回设置，找到"开发者选项"

2. **开启USB调试**
   - 开发者选项 → USB调试 → 开启
   - 连接电脑时选择"允许USB调试"

3. **保持屏幕常亮**
   - 开发者选项 → 保持唤醒状态 → 开启

### 模拟器设置

推荐使用以下Android模拟器：
- **夜神模拟器**: 默认地址 `127.0.0.1:62001`
- **雷电模拟器**: 默认地址 `127.0.0.1:5555`
- **BlueStacks**: 需要在设置中开启ADB

### 连接测试

```bash
# 检查设备连接
adb devices

# 应该显示类似输出：
# List of devices attached
# 127.0.0.1:5555    device
```

## 🎮 界面使用说明

### 简化功能界面

#### 设备连接区域
- **设备地址**: 输入Android设备或模拟器地址
- **连接设备**: 点击连接到指定设备
- **连接状态**: 显示当前连接状态

#### 任务控制区域
- **开始执行**: 开始运行自动化任务
- **停止执行**: 中断正在执行的任务
- **验证配置**: 检查配置文件是否正确
- **进度条**: 显示任务执行进度

#### 任务列表
- 显示所有配置的任务
- 执行时会高亮当前任务

#### 执行日志
- 实时显示执行过程
- 包含详细的调试信息

## ⚙️ 配置文件说明

### pipeline.json 结构

```json
{
    "任务名称": {
        "action": "动作类型",
        "recognition": "识别方式",
        "template": "模板图片.png",
        "expected": "期望文字",
        "input_text": "输入内容",
        "package": "应用包名",
        "next": ["下一个任务"]
    }
}
```

### 支持的动作类型

| 动作 | 说明 | 必需参数 |
|------|------|----------|
| `StartApp` | 启动应用 | `package` |
| `Click` | 点击操作 | `recognition` + `template`或`expected` |
| `InputText` | 输入文本 | `input_text` |
| `ReadEmailFromFile` | 读取邮箱文件 | `file_name`, `line_number` |

### 支持的识别方式

| 识别方式 | 说明 | 参数 |
|----------|------|------|
| `TemplateMatch` | 图片模板匹配 | `template`: 图片文件名 |
| `OCR` | 文字识别 | `expected`: 期望识别的文字 |

## 🔧 常见问题解决

### 1. 设备连接失败

**问题**: 显示"设备连接失败"
**解决方案**:
```bash
# 1. 检查ADB是否安装
adb version

# 2. 检查设备是否连接
adb devices

# 3. 重启ADB服务
adb kill-server
adb start-server

# 4. 重新连接设备
adb connect 127.0.0.1:5555
```

### 2. 图像识别失败

**问题**: 无法找到界面元素
**解决方案**:
1. 检查模板图片是否存在于 `resource/image/` 目录
2. 确保设备分辨率与模板图片匹配
3. 查看 `debug_images/` 目录中的调试截图
4. 调整识别阈值或重新截取模板图片

### 3. OCR识别不准确

**问题**: 文字识别错误或失败
**解决方案**:
1. 确保Tesseract正确安装
2. 检查文字是否清晰可见
3. 尝试调整OCR置信度阈值
4. 使用更精确的识别区域

### 4. 任务执行卡住

**问题**: 任务执行过程中停止响应
**解决方案**:
1. 检查设备屏幕是否锁定
2. 确保应用界面没有弹窗遮挡
3. 增加任务间的等待时间
4. 查看执行日志了解具体错误

## 🛠️ 高级配置

### 调整识别参数

在 `opencv_recognition.py` 中：
```python
# 模板匹配阈值（0-1，越高越严格）
self.template_threshold = 0.8

# OCR识别置信度（0-100，越高越严格）
self.ocr_confidence = 60
```

### 调整执行参数

在 `simple_automation_engine.py` 中：
```python
# 默认等待时间（秒）
self.default_wait_time = 1.0

# 最大重试次数
self.max_retry_times = 3

# 截图间隔（秒）
self.screenshot_interval = 0.5
```

### 自定义动作

添加新的自定义动作：
```python
def _execute_custom_action(self, task_config: Dict[str, Any]) -> bool:
    """自定义动作实现"""
    # 实现你的自定义逻辑
    return True

# 在 _execute_single_task 方法中添加：
elif action == "CustomAction":
    return self._execute_custom_action(task_config)
```

## 📁 项目结构

```
demo/
├── resource/                          # 资源文件
│   ├── image/                        # 模板图片
│   ├── pipeline/
│   │   ├── pipeline.json            # 任务配置
│   │   └── 邮箱.txt                 # 数据文件
│   └── config/settings.json         # 系统配置
├── opencv_recognition.py             # 图像识别模块
├── android_controller.py             # 设备控制模块
├── simple_automation_engine.py       # 任务执行引擎
├── simple_automation_ui.py           # 简化UI界面
├── maa_ui_design.py                  # 设计UI界面
├── main.py                           # 主启动文件
├── debug_images/                     # 调试截图（自动生成）
└── 新手使用指南.md                   # 本文档
```

## 🎓 学习路径

### 新手入门
1. 先使用现有配置运行系统
2. 理解pipeline.json的基本结构
3. 学会修改简单的任务参数

### 进阶使用
1. 学习OpenCV图像识别原理
2. 了解ADB命令的使用
3. 自定义任务配置

### 高级开发
1. 修改和扩展识别算法
2. 添加新的动作类型
3. 优化执行性能

## 💡 最佳实践

1. **逐步调试**: 先测试单个任务，再运行完整序列
2. **保存截图**: 利用调试截图功能排查问题
3. **备份配置**: 修改配置前先备份原文件
4. **日志分析**: 仔细查看执行日志了解问题原因
5. **参数调优**: 根据实际设备调整识别和执行参数

## 🆘 获取帮助

如果遇到问题：
1. 查看执行日志中的错误信息
2. 检查 `debug_images/` 目录中的截图
3. 参考本文档的常见问题部分
4. 使用"测试配置"功能验证系统状态

---

**祝您使用愉快！** 🎉
